'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronDown } from '@untitled-ui/icons-react';
import { cn } from '../../lib/utils';

interface DropdownMenuItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface DropdownMenuProps {
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  items: DropdownMenuItem[];
  className?: string;
  isAICoach?: boolean;
}

export function DropdownMenu({ label, icon: Icon, items, className = '', isAICoach = false }: DropdownMenuProps) {
  const pathname = usePathname();

  // Check if any of the dropdown items are active
  const isActive = items.some(item => pathname === item.href);

  // Keep dropdown open if any item is active
  const [isOpen, setIsOpen] = useState(isActive);

  return (
    <div className={cn('relative', className)}>
      {/* Dropdown Trigger */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
          isOpen
            ? 'bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white'
            : 'text-gray-900 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-900 hover:text-gray-900 dark:hover:text-white'
        )}
        data-rounded="default"
      >
        <div className="flex items-center gap-3">
          <Icon className="h-5 w-5" />
          {label}
        </div>
        <ChevronDown
          className={cn(
            'h-4 w-4 transition-transform duration-300 ease-in-out',
            isOpen ? 'rotate-180' : ''
          )}
        />
      </button>

      {/* Dropdown Menu with smooth animation */}
      <div
        className={cn(
          'overflow-hidden transition-all duration-300 ease-in-out',
          isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <div className="mt-1 space-y-1">
          {items.map((item) => {
            const isItemActive = pathname === item.href;

            // Special styling for AI Coach
            const isAICoach = item.name === 'AI Coach';

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center pl-12 pr-3 py-2 text-sm rounded-lg transition-all duration-200',
                  isItemActive
                    ? isAICoach
                      ? 'bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white font-medium'
                      : 'bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white font-medium'
                    : isAICoach
                      ? 'text-gray-900 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-900 hover:text-gray-900 dark:hover:text-white'
                      : 'text-gray-900 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-900 hover:text-gray-900 dark:hover:text-white'
                )}
              >
                <item.icon className="mr-3 h-4 w-4" />
                <span>{item.name}</span>
                {isAICoach && (
                  <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-500 text-white">
                    New
                  </span>
                )}
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
}

export default DropdownMenu;
