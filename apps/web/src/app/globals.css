@import url('https://api.fontshare.com/v2/css?f[]=general-sans@500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark mode variables */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

/* Removed automatic dark mode media query to prevent hydration issues */
/* Dark mode will be controlled manually via class-based approach */

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Consistent border radius for all elements with data-rounded attribute */
[data-rounded="default"] {
  border-radius: 0.5rem; /* 8px - consistent radius */
}

/* Ensure all interactive elements have consistent radius */
button, input, select, textarea, .card, .badge {
  border-radius: 0.5rem;
}

/* Override any conflicting radius classes */
.rounded-lg {
  border-radius: 0.5rem !important;
}

.rounded-xl {
  border-radius: 0.75rem !important;
}

.rounded-2xl {
  border-radius: 1rem !important;
}

/* Custom slider styling */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #007FFF;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #007FFF;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-webkit-slider-track {
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
}

.slider::-moz-range-track {
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  border: none;
}

/* Dark mode slider styling */
.dark .slider::-webkit-slider-thumb {
  border: 2px solid #374151;
}

.dark .slider::-moz-range-thumb {
  border: 2px solid #374151;
}

.dark .slider::-webkit-slider-track {
  background: #374151;
}

.dark .slider::-moz-range-track {
  background: #374151;
}

/* Additional base styles */
@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}